'use client';

import React from 'react';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';
import styles from './CloudMigrationQuestionAndAnswers.module.css';

interface Answer {
  id: string;
  name: string;
  value: number;
}

interface SubQuestion {
  name: string;
  value: number;
}

interface Question {
  number: number;
  name: string;
  type: 'mcq' | 'range' | 'draggable';
  answers: Answer[];
  sub_question: SubQuestion[];
}

interface CloudMigrationQuestionAndAnswersProps {
  sectionIndex: number;
  sectionQuestions: Question[];
  sectionData: [string | null, number | null][];
  sectionError: (boolean | null)[];
  handleData: (
    sectionIndex: number,
    questionIndex: number,
    name: string,
    value: number,
  ) => void;
  handleError: (
    sectionIndex: number,
    questionIndex: number,
    value: boolean,
  ) => void;
}

export default function CloudMigrationQuestionAndAnswers({
  sectionIndex,
  sectionQuestions,
  sectionData,
  sectionError,
  handleData,
  handleError,
}: CloudMigrationQuestionAndAnswersProps) {
  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl-1024']})`,
  });

  // Note: subAnswer state removed as cloud migration doesn't use sub-questions

  // Keyboard event handler
  const handleKeyDown = (event: React.KeyboardEvent, callback: () => void) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      callback();
    }
  };

  // Handle multi-select questions (checkboxes) for cloud migration
  function handleMultiSelect(
    questionIndex: number,
    answerName: string,
    answerValue: number,
    isChecked: boolean,
  ) {
    const currentData = sectionData[questionIndex];
    let currentAnswers = currentData?.[0] ? currentData[0].split(',') : [];
    let currentValues = currentData?.[1] || 0;

    if (isChecked) {
      // Add answer if not already present
      if (!currentAnswers.includes(answerName)) {
        currentAnswers.push(answerName);
        currentValues += answerValue;
      }
    } else {
      // Remove answer
      currentAnswers = currentAnswers.filter(answer => answer !== answerName);
      currentValues -= answerValue;
    }

    const finalAnswerString =
      currentAnswers.length > 0 ? currentAnswers.join(',') : null;
    const finalValue = Math.max(0, currentValues); // Ensure non-negative

    handleError(sectionIndex, questionIndex, false);
    handleData(
      sectionIndex,
      questionIndex,
      finalAnswerString || '',
      finalValue,
    );
  }

  // Handle single select questions (radio buttons)
  function handleSingleSelect(
    questionIndex: number,
    answerName: string,
    answerValue: number,
  ) {
    handleError(sectionIndex, questionIndex, false);
    handleData(sectionIndex, questionIndex, answerName, answerValue);
  }

  // Check if answer is selected for multi-select questions
  function isAnswerSelected(
    questionIndex: number,
    answerName: string,
  ): boolean {
    const currentData = sectionData[questionIndex];
    if (!currentData?.[0]) return false;
    const selectedAnswers = currentData[0].split(',');
    return selectedAnswers.includes(answerName);
  }

  // Determine if question supports multiple selections based on question content
  function isMultiSelectQuestion(question: Question): boolean {
    const questionName = question.name.toLowerCase();
    return (
      questionName.includes('which cloud environments') ||
      questionName.includes('what type of workloads') ||
      questionName.includes('what is the main purpose') ||
      questionName.includes('select all that apply') ||
      questionName.includes('multiple') ||
      questionName.includes(
        'do you have any specific compliance or regulatory requirements that your cloud migration needs to meet?',
      ) ||
      question.answers.some(answer => answer.name.includes('Multiple'))
    );
  }

  function getSliderValueFromAnswer(
    questionIndex: number,
    answerName: string,
  ): number {
    const question = sectionQuestions[questionIndex];
    if (!question || !question.answers) return 0;

    if (question.type === 'draggable') {
      const answerIndex = question.answers.findIndex(
        ans => ans.name === answerName,
      );
      if (answerIndex === -1) {
        return 0;
      }

      // Use integer values to avoid floating point issues with HTML range input
      const sliderValue = answerIndex * 10;
      return sliderValue;
    } else {
      // Legacy logic for range questions
      const answerIndex = question.answers.findIndex(
        ans => ans.name === answerName,
      );
      return answerIndex * 25;
    }
  }

  function getAnswerName(questionIndex: number, value: number): string {
    const question = sectionQuestions[questionIndex];
    if (!question || !question.answers) return '';

    if (question.type === 'draggable') {
      // For draggable questions, find the closest answer based on slider position
      // Using integer scale (each step is 10 units)
      let ind = Math.round(value / 10);

      // Ensure index is within bounds
      ind = Math.max(0, Math.min(ind, question.answers.length - 1));

      const answerName = question.answers[ind].name;
      return answerName;
    } else {
      // Legacy logic for range questions
      let ind = Math.round(value / 25);
      // Check if the calculated index is within bounds
      if (ind >= question.answers.length) {
        console.warn(
          `Answer index ${ind} exceeds answers array length ${question.answers.length}`,
        );
        return '';
      }

      return question.answers[ind].name;
    }
  }

  return (
    <>
      {sectionQuestions.map((question, questionIndex) => (
        <div key={questionIndex} className={styles.container}>
          <div className={styles.question_container}>
            <div
              className={
                sectionError[questionIndex]
                  ? `${styles.question_number} ${styles.error_message}`
                  : styles.question_number
              }
            >
              {question.number < 10 ? '0' : ''}
              {question.number}
              {'.'}
            </div>
            <div className={styles.question_name}>{question.name}</div>
          </div>

          {question.type === 'mcq' ? (
            <div className={styles.mcqs_container}>
              {question.answers.map((ans, answerIndex) => {
                const isMultiSelect = isMultiSelectQuestion(question);
                const isSelected = isMultiSelect
                  ? isAnswerSelected(questionIndex, ans.name)
                  : sectionData[questionIndex]?.[0] === ans.name;

                return (
                  <label
                    key={answerIndex}
                    className={
                      isSelected
                        ? `${styles.mcq} ${styles.selected_mcq}`
                        : styles.mcq
                    }
                    htmlFor={`${ans.id}_${sectionIndex}_${questionIndex}`}
                    tabIndex={0}
                    onKeyDown={e =>
                      handleKeyDown(e, () => {
                        if (isMultiSelect) {
                          handleMultiSelect(
                            questionIndex,
                            ans.name,
                            ans.value,
                            !isSelected,
                          );
                        } else {
                          handleSingleSelect(
                            questionIndex,
                            ans.name,
                            ans.value,
                          );
                        }
                      })
                    }
                  >
                    <input
                      type={isMultiSelect ? 'checkbox' : 'radio'}
                      id={`${ans.id}_${sectionIndex}_${questionIndex}`}
                      name={`question_${sectionIndex}_${questionIndex}`}
                      value={ans.id}
                      checked={isSelected}
                      onChange={e => {
                        if (isMultiSelect) {
                          handleMultiSelect(
                            questionIndex,
                            ans.name,
                            ans.value,
                            e.target.checked,
                          );
                        } else {
                          handleSingleSelect(
                            questionIndex,
                            ans.name,
                            ans.value,
                          );
                        }
                      }}
                    />
                    {ans.name}
                  </label>
                );
              })}
            </div>
          ) : question.type === 'draggable' ? (
            // Range/slider questions (similar to AI readiness but with cloud migration styling)
            <>
              {!isTablet ? (
                <div className={styles.draggable_container}>
                  <input
                    type="range"
                    id={`range_${sectionIndex}_${questionIndex}`}
                    name={`range_${sectionIndex}_${questionIndex}`}
                    key={`slider_${sectionIndex}_${questionIndex}_${sectionData[questionIndex]?.[1] || 0}`}
                    min="0"
                    max={
                      question.type === 'draggable'
                        ? (question.answers.length - 1) * 10
                        : 100
                    }
                    step={question.type === 'draggable' ? 10 : 25}
                    className={styles.draggable_input}
                    value={sectionData[questionIndex]?.[1] || 0}
                    style={{
                      background: `linear-gradient(to right, #30ad43 0%, #30ad43 ${((sectionData[questionIndex]?.[1] || 0) / ((question.answers.length - 1) * 10)) * 100}%, #e0e0e0 ${((sectionData[questionIndex]?.[1] || 0) / ((question.answers.length - 1) * 10)) * 100}%, #e0e0e0 100%)`,
                    }}
                    onChange={e => {
                      handleError(sectionIndex, questionIndex, false);
                      const sliderValue = Number(e.target.value);
                      const answerName = getAnswerName(
                        questionIndex,
                        sliderValue,
                      );

                      handleData(
                        sectionIndex,
                        questionIndex,
                        answerName,
                        sliderValue,
                      );
                    }}
                    onKeyDown={e => {
                      const currentValue = sectionData[questionIndex]?.[1] || 0;
                      const stepSize = question.type === 'draggable' ? 10 : 25;
                      const maxValue =
                        question.type === 'draggable'
                          ? (question.answers.length - 1) * 10
                          : 100;
                      let newValue = currentValue;

                      if (e.key === 'ArrowLeft' || e.key === 'ArrowDown') {
                        e.preventDefault();
                        newValue = Math.max(0, currentValue - stepSize);
                      } else if (
                        e.key === 'ArrowRight' ||
                        e.key === 'ArrowUp'
                      ) {
                        e.preventDefault();
                        newValue = Math.min(maxValue, currentValue + stepSize);
                      } else if (e.key === 'Home') {
                        e.preventDefault();
                        newValue = 0;
                      } else if (e.key === 'End') {
                        e.preventDefault();
                        newValue = maxValue;
                      }

                      if (newValue !== currentValue) {
                        handleError(sectionIndex, questionIndex, false);
                        const answerName = getAnswerName(
                          questionIndex,
                          newValue,
                        );

                        handleData(
                          sectionIndex,
                          questionIndex,
                          answerName,
                          newValue,
                        );
                      }
                    }}
                    tabIndex={0}
                    role="slider"
                    aria-valuemin={0}
                    aria-valuemax={
                      question.type === 'draggable'
                        ? (question.answers.length - 1) * 10
                        : 100
                    }
                    aria-valuenow={sectionData[questionIndex]?.[1] || 0}
                    aria-valuetext={sectionData[questionIndex]?.[0] || ''}
                  />
                  <div className={styles.draggable_wrapper}>
                    {question.answers.map((ans, answerIndex) => (
                      <div
                        key={answerIndex}
                        className={
                          sectionData[questionIndex]?.[0] === ans.name
                            ? `${styles.draggable_label} ${styles.selected_draggable_label}`
                            : styles.draggable_label
                        }
                        onClick={() => {
                          handleError(sectionIndex, questionIndex, false);
                          const sliderValue = getSliderValueFromAnswer(
                            questionIndex,
                            ans.name,
                          );
                          handleData(
                            sectionIndex,
                            questionIndex,
                            ans.name,
                            sliderValue,
                          );
                        }}
                        onKeyDown={e =>
                          handleKeyDown(e, () => {
                            handleError(sectionIndex, questionIndex, false);
                            const sliderValue = getSliderValueFromAnswer(
                              questionIndex,
                              ans.name,
                            );
                            handleData(
                              sectionIndex,
                              questionIndex,
                              ans.name,
                              sliderValue,
                            );
                          })
                        }
                        tabIndex={0}
                        role="button"
                        aria-pressed={
                          sectionData[questionIndex]?.[0] === ans.name
                        }
                      >
                        {ans.name}
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className={styles.number_wrapper}>
                  <div className={styles.draggable_container_tablet}>
                    {question.answers.map((ans, answerIndex) => (
                      <label
                        key={answerIndex}
                        className={
                          sectionData[questionIndex]?.[0] === ans.name
                            ? `${styles.number} ${styles.selected_number}`
                            : styles.number
                        }
                        htmlFor={`${ans.id}_tablet`}
                        tabIndex={0}
                        onKeyDown={e =>
                          handleKeyDown(e, () => {
                            handleError(sectionIndex, questionIndex, false);
                            const sliderValue = getSliderValueFromAnswer(
                              questionIndex,
                              ans.name,
                            );
                            handleData(
                              sectionIndex,
                              questionIndex,
                              ans.name,
                              sliderValue,
                            );
                          })
                        }
                      >
                        <input
                          type="radio"
                          id={`${ans.id}_tablet`}
                          name={`question_${sectionIndex}_${questionIndex}`}
                          value={ans.id}
                          checked={sectionData[questionIndex]?.[0] === ans.name}
                          onChange={() => {
                            handleError(sectionIndex, questionIndex, false);
                            const sliderValue = getSliderValueFromAnswer(
                              questionIndex,
                              ans.name,
                            );
                            handleData(
                              sectionIndex,
                              questionIndex,
                              ans.name,
                              sliderValue,
                            );
                          }}
                        />
                        {answerIndex + 1}
                      </label>
                    ))}
                  </div>
                  <div className={styles.number_label}>
                    <span>{question.answers[0]?.name}</span>
                    <span>
                      {question.answers[question.answers.length - 1]?.name}
                    </span>
                  </div>
                </div>
              )}
            </>
          ) : null}
        </div>
      ))}
    </>
  );
}
